/**
 * Tablet-Optimized Form Components
 * Designed for touch interactions with proper spacing and sizing
 */

/* Base Form Styles */
.tablet-form {
  @apply w-full max-w-none;
}

.tablet-form-container {
  @apply p-4 tablet-mini:p-6 tablet:p-8 tablet-large:p-10;
}

/* Form Groups */
.tablet-form-group {
  @apply mb-4 tablet-mini:mb-5 tablet:mb-6;
}

.tablet-form-group-inline {
  @apply flex flex-col tablet-mini:flex-row tablet-mini:items-center tablet-mini:gap-4;
}

/* Labels */
.tablet-form-label {
  @apply block text-sm tablet-mini:text-base font-medium text-gray-700 mb-2 tablet-mini:mb-3;
}

.tablet-form-label-required::after {
  content: ' *';
  @apply text-red-500;
}

.tablet-form-label-inline {
  @apply tablet-mini:w-1/3 tablet-mini:mb-0;
}

/* Input Fields */
.tablet-form-input {
  @apply w-full px-3 tablet-mini:px-4 py-3 tablet-mini:py-4 text-base border border-gray-300 rounded-lg tablet:rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200;
  min-height: 44px;
}

.tablet-form-input:focus {
  @apply outline-none ring-2 ring-blue-500 border-blue-500;
}

.tablet-form-input.error {
  @apply border-red-500 ring-red-500;
}

.tablet-form-input.success {
  @apply border-green-500 ring-green-500;
}

/* Textarea */
.tablet-form-textarea {
  @apply tablet-form-input resize-none;
  min-height: 100px;
}

.tablet-form-textarea-large {
  min-height: 150px;
}

/* Select Dropdowns */
.tablet-form-select {
  @apply tablet-form-input appearance-none bg-white;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Checkboxes and Radio Buttons */
.tablet-form-checkbox,
.tablet-form-radio {
  @apply w-5 h-5 tablet-mini:w-6 tablet-mini:h-6 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2;
}

.tablet-form-checkbox-label,
.tablet-form-radio-label {
  @apply flex items-center gap-3 tablet-mini:gap-4 cursor-pointer text-sm tablet-mini:text-base;
  min-height: 44px;
}

/* Switch Toggle */
.tablet-form-switch {
  @apply relative inline-flex h-6 w-11 tablet-mini:h-7 tablet-mini:w-12 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.tablet-form-switch.checked {
  @apply bg-blue-600;
}

.tablet-form-switch-thumb {
  @apply inline-block h-4 w-4 tablet-mini:h-5 tablet-mini:w-5 transform rounded-full bg-white transition-transform;
}

.tablet-form-switch.checked .tablet-form-switch-thumb {
  @apply translate-x-5 tablet-mini:translate-x-6;
}

/* Buttons */
.tablet-form-button {
  @apply px-4 tablet-mini:px-6 py-3 tablet-mini:py-4 text-base tablet:text-lg font-medium rounded-lg tablet:rounded-xl transition-all duration-200;
  min-height: 44px;
  min-width: 44px;
}

.tablet-form-button-primary {
  @apply tablet-form-button bg-blue-600 text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.tablet-form-button-secondary {
  @apply tablet-form-button bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
}

.tablet-form-button-danger {
  @apply tablet-form-button bg-red-600 text-white hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
}

.tablet-form-button-outline {
  @apply tablet-form-button border-2 border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* Button Groups */
.tablet-form-button-group {
  @apply flex flex-col tablet-mini:flex-row gap-3 tablet-mini:gap-4;
}

.tablet-form-button-group-right {
  @apply tablet-form-button-group tablet-mini:justify-end;
}

/* Error Messages */
.tablet-form-error {
  @apply text-red-600 text-sm tablet-mini:text-base mt-2;
}

.tablet-form-error-icon {
  @apply inline-block w-4 h-4 mr-2;
}

/* Success Messages */
.tablet-form-success {
  @apply text-green-600 text-sm tablet-mini:text-base mt-2;
}

/* Help Text */
.tablet-form-help {
  @apply text-gray-500 text-sm tablet-mini:text-base mt-2;
}

/* Form Sections */
.tablet-form-section {
  @apply mb-6 tablet-mini:mb-8 tablet:mb-10;
}

.tablet-form-section-title {
  @apply text-lg tablet-mini:text-xl tablet:text-2xl font-semibold text-gray-900 mb-4 tablet-mini:mb-6;
}

.tablet-form-section-description {
  @apply text-gray-600 text-sm tablet-mini:text-base mb-4 tablet-mini:mb-6;
}

/* Form Cards */
.tablet-form-card {
  @apply bg-white rounded-lg tablet:rounded-xl shadow-sm border border-gray-200 p-4 tablet-mini:p-6 tablet:p-8;
}

.tablet-form-card-header {
  @apply border-b border-gray-200 pb-4 tablet-mini:pb-6 mb-4 tablet-mini:mb-6;
}

.tablet-form-card-title {
  @apply text-lg tablet-mini:text-xl font-semibold text-gray-900;
}

.tablet-form-card-description {
  @apply text-gray-600 text-sm tablet-mini:text-base mt-2;
}

/* Grid Layouts */
.tablet-form-grid {
  @apply grid grid-cols-1 tablet-mini:grid-cols-2 tablet:grid-cols-3 gap-4 tablet-mini:gap-6;
}

.tablet-form-grid-2 {
  @apply grid grid-cols-1 tablet-mini:grid-cols-2 gap-4 tablet-mini:gap-6;
}

.tablet-form-grid-3 {
  @apply grid grid-cols-1 tablet-mini:grid-cols-2 tablet:grid-cols-3 gap-4 tablet-mini:gap-6;
}

.tablet-form-grid-4 {
  @apply grid grid-cols-1 tablet-mini:grid-cols-2 tablet:grid-cols-3 tablet-large:grid-cols-4 gap-4 tablet-mini:gap-6;
}

/* File Upload */
.tablet-form-file-upload {
  @apply border-2 border-dashed border-gray-300 rounded-lg tablet:rounded-xl p-6 tablet-mini:p-8 text-center hover:border-gray-400 transition-colors duration-200;
}

.tablet-form-file-upload.dragover {
  @apply border-blue-500 bg-blue-50;
}

.tablet-form-file-upload-icon {
  @apply w-8 h-8 tablet-mini:w-10 tablet-mini:h-10 mx-auto text-gray-400 mb-4;
}

.tablet-form-file-upload-text {
  @apply text-sm tablet-mini:text-base text-gray-600;
}

/* Date Picker */
.tablet-form-date-picker {
  @apply tablet-form-input;
}

.tablet-form-date-range {
  @apply flex flex-col tablet-mini:flex-row gap-3 tablet-mini:gap-4;
}

/* Multi-select */
.tablet-form-multiselect {
  @apply border border-gray-300 rounded-lg tablet:rounded-xl p-2 tablet-mini:p-3 max-h-40 overflow-y-auto;
}

.tablet-form-multiselect-option {
  @apply tablet-form-checkbox-label p-2 tablet-mini:p-3 hover:bg-gray-50 rounded;
}

/* Form Validation States */
.tablet-form-field-valid {
  @apply border-green-500 ring-green-500;
}

.tablet-form-field-invalid {
  @apply border-red-500 ring-red-500;
}

.tablet-form-field-warning {
  @apply border-yellow-500 ring-yellow-500;
}

/* Loading States */
.tablet-form-loading {
  @apply opacity-50 pointer-events-none;
}

.tablet-form-spinner {
  @apply inline-block w-4 h-4 tablet-mini:w-5 tablet-mini:h-5 border-2 border-current border-t-transparent rounded-full animate-spin;
}

/* Responsive Adjustments */
@media (orientation: landscape) and (max-height: 600px) {
  .tablet-form-container {
    @apply p-3;
  }
  
  .tablet-form-group {
    @apply mb-3;
  }
  
  .tablet-form-input {
    @apply py-2;
  }
}

/* Touch-specific improvements */
@media (hover: none) and (pointer: coarse) {
  .tablet-form-input,
  .tablet-form-select,
  .tablet-form-textarea {
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .tablet-form-button {
    min-height: 44px;
    min-width: 44px;
  }
}
