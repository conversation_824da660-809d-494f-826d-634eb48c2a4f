/**
 * Shared Component Styles
 * This file contains common styles for components that can be reused across the application
 */

/* Common Form Styles */
.form-container {
  @apply w-full flex flex-col gap-4 bg-white rounded-lg p-6;
}

.form-group {
  @apply flex flex-col gap-2;
}

.form-select {
  @apply w-full px-4 py-2 border border-gray-200 rounded-lg text-sm text-gray-800 focus:border-[var(--primary)] outline-none appearance-none bg-white;
}

.form-textarea {
  @apply w-full px-4 py-2 border border-gray-200 rounded-lg text-sm text-gray-800 focus:border-[var(--primary)] outline-none min-h-[100px] resize-none;
}

.form-checkbox {
  @apply w-4 h-4 text-[var(--primary)] bg-white border border-gray-300 rounded focus:ring-[var(--primary)];
}

.form-radio {
  @apply w-4 h-4 text-[var(--primary)] bg-white border border-gray-300 rounded-full focus:ring-[var(--primary)];
}

/* Common Button Styles */
.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2;
}

.btn-primary {
  @apply bg-[var(--primary)] text-white hover:bg-[var(--primary-hover)];
}

.btn-secondary {
  @apply bg-white text-[var(--primary)] border border-[var(--primary)] hover:bg-gray-50;
}

.btn-danger {
  @apply bg-red-500 text-white hover:bg-red-600;
}

.btn-sm {
  @apply px-3 py-1 text-sm;
}

.btn-lg {
  @apply px-6 py-3 text-lg;
}

/* Common Card Styles */
.card {
  @apply bg-white rounded-lg shadow-sm p-6;
}

.card-header {
  @apply flex items-center justify-between mb-4;
}

.card-title {
  @apply text-lg font-semibold text-gray-900;
}

.card-body {
  @apply flex flex-col gap-4;
}

.card-footer {
  @apply flex justify-end gap-4 mt-4 pt-4 border-t border-gray-100;
}

/* Common Table Styles */
.table-container {
  @apply w-full overflow-x-auto rounded-lg;
}

.table-header {
  @apply w-full bg-gray-50 p-4 border border-gray-200 rounded-t-lg flex justify-between items-center;
}

.table-wrapper {
  @apply w-full overflow-x-auto;
}

.table {
  @apply w-full border-collapse;
}

.table th {
  @apply bg-gray-50 px-4 py-3 text-left font-medium text-gray-800 border border-gray-200;
}

.table td {
  @apply px-4 py-3 border border-gray-200;
}

.table tr:hover {
  @apply bg-gray-50;
}

.table-base {
  @apply min-w-full divide-y divide-gray-200;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.header-left {
  @apply flex items-center gap-2.5 text-xs font-bold text-[var(--gray-700)];
}

.link-cell {
  @apply w-[40%];
}

.link-content {
  @apply flex flex-col gap-2;
}

.description-row {
  @apply flex items-center gap-1;
}

/* Common Status Indicators */
.status-indicator {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-success {
  @apply bg-green-100 text-green-800;
}

.status-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.status-error {
  @apply bg-red-100 text-red-800;
}

.status-info {
  @apply bg-blue-100 text-blue-800;
}

/* Common Modal/Popup Styles */
.modal-container {
  @apply flex flex-col bg-white rounded-lg shadow-lg max-w-md w-full mx-auto;
}

.modal-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.modal-title {
  @apply text-lg font-semibold text-gray-900;
}

.modal-body {
  @apply p-4;
}

.modal-footer {
  @apply flex justify-end gap-4 p-4 border-t border-gray-200;
}

/* Common Search and Filter Styles */
.filter-dropdown {
  @apply relative min-w-[200px];
}

.search-container {
  @apply flex items-center gap-2 bg-white rounded-lg border border-[var(--gray-200)];
  height: var(--input-height);
}

.search-input {
  @apply flex-grow border-none outline-none font-roboto text-base text-[var(--gray-700)];
}

.search-button {
  @apply bg-[var(--primary)] text-white font-medium text-sm px-4 py-2 rounded-md cursor-pointer transition-colors duration-300 hover:bg-[var(--primary-hover)] active:bg-[var(--primary-active)];
  height: calc(var(--input-height) - 8px);
}

.search-box {
  @apply flex items-center bg-white border border-[var(--gray-200)] rounded-lg px-4 py-2;
  height: var(--input-height);
  width: var(--search-box-width);
}

.dropdown {
  @apply relative bg-[var(--background)] rounded-[var(--radius-md)] p-3;
}

.dropdown-container {
  @apply p-3 w-full relative text-left border-none cursor-pointer block;
}

/* Social Item Styles */
.social-item {
  @apply flex items-center gap-3 p-3 bg-[var(--background)] rounded-full cursor-pointer transition-colors duration-200 shadow-sm;
  max-width: 100%;
}

.social-item:hover {
  @apply bg-[var(--primary-light)];
}

.social-item span {
  @apply font-roboto text-base font-medium leading-6 text-gray-700 truncate;
}

/* Badge and Tag Styles */
.badge {
  @apply bg-[var(--primary)] text-white px-2.5 py-1 rounded-full text-xs font-medium;
}

.description {
  @apply text-[var(--gray-700)] font-roboto font-bold text-xs leading-[18px];
}

.tags-container {
  @apply flex gap-2 flex-wrap;
}

/* Pagination Styles */
.pagination {
  @apply flex justify-between items-center mt-6;
}

.page-numbers {
  @apply flex gap-6 items-center;
}

.page-nav {
  @apply w-8 h-8 border border-[var(--gray-200)] rounded-lg flex items-center justify-center cursor-pointer bg-transparent;
}

.page-num {
  @apply w-8 h-8 border border-[var(--gray-200)] rounded-lg bg-transparent text-xs text-[var(--gray-800)] cursor-pointer;
}

.page-num.active {
  @apply bg-[var(--gray-100)];
}

.page-info {
  @apply flex items-center gap-4 text-[var(--secondary)] text-xs;
}

.show-entries {
  @apply flex items-center gap-2.5 p-2.5 border border-[var(--gray-200)] rounded-lg text-[var(--gray-800)] text-xs cursor-pointer;
}

/* Common Responsive Styles */
/* Mobile (below 768px) */
@media (max-width: 767px) {
  .filter-dropdown {
    @apply w-full;
  }

  .table-header {
    @apply flex-col items-start gap-2;
  }

  .modal-container {
    @apply max-w-full mx-4;
  }

  .social-item {
    @apply rounded-xl shadow-none;
  }

  .social-item span {
    @apply text-sm;
  }

  .search-container {
    @apply flex-col;
  }

  .search-input {
    @apply w-full;
  }
}

/* Tablet Mini (iPad Mini) - 768px to 1023px */
@media (min-width: 768px) and (max-width: 1023px) {
  .filter-dropdown {
    @apply min-w-48;
  }

  .table-header {
    @apply flex-row items-center gap-3;
  }

  .modal-container {
    @apply max-w-2xl mx-6;
  }

  .social-item {
    @apply rounded-xl shadow-sm p-3;
  }

  .social-item span {
    @apply text-sm;
  }

  .search-container {
    @apply flex-row gap-3;
  }

  .search-input {
    @apply min-w-64;
  }

  .btn-primary,
  .btn-secondary {
    @apply px-4 py-3 text-base;
  }
}

/* Tablet (iPad) - 1024px to 1279px */
@media (min-width: 1024px) and (max-width: 1279px) {
  .filter-dropdown {
    @apply min-w-52;
  }

  .table-header {
    @apply flex-row items-center gap-4;
  }

  .modal-container {
    @apply max-w-3xl mx-8;
  }

  .social-item {
    @apply rounded-xl shadow-sm p-4;
  }

  .social-item span {
    @apply text-base;
  }

  .search-container {
    @apply flex-row gap-4;
  }

  .search-input {
    @apply min-w-72;
  }

  .btn-primary,
  .btn-secondary {
    @apply px-5 py-3 text-base;
  }
}

/* Large Tablet - 1280px and up */
@media (min-width: 1280px) {
  .filter-dropdown {
    @apply min-w-56;
  }

  .table-header {
    @apply flex-row items-center gap-5;
  }

  .modal-container {
    @apply max-w-4xl mx-10;
  }

  .social-item {
    @apply rounded-2xl shadow-md p-5;
  }

  .social-item span {
    @apply text-lg;
  }

  .search-container {
    @apply flex-row gap-5;
  }

  .search-input {
    @apply min-w-80;
  }

  .btn-primary,
  .btn-secondary {
    @apply px-6 py-4 text-lg;
  }
}
