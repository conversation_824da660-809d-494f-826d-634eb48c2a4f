/**
 * Tablet Responsive Utilities
 * Optimized for iPad Mini, iPad, and tablet devices
 */

/* Tablet Grid Systems */
.tablet-grid-1 { @apply tablet:grid-cols-1; }
.tablet-grid-2 { @apply tablet:grid-cols-2; }
.tablet-grid-3 { @apply tablet:grid-cols-3; }
.tablet-grid-4 { @apply tablet:grid-cols-4; }
.tablet-grid-5 { @apply tablet:grid-cols-5; }
.tablet-grid-6 { @apply tablet:grid-cols-6; }

.tablet-mini-grid-1 { @apply tablet-mini:grid-cols-1; }
.tablet-mini-grid-2 { @apply tablet-mini:grid-cols-2; }
.tablet-mini-grid-3 { @apply tablet-mini:grid-cols-3; }

.tablet-large-grid-1 { @apply tablet-large:grid-cols-1; }
.tablet-large-grid-2 { @apply tablet-large:grid-cols-2; }
.tablet-large-grid-3 { @apply tablet-large:grid-cols-3; }
.tablet-large-grid-4 { @apply tablet-large:grid-cols-4; }
.tablet-large-grid-5 { @apply tablet-large:grid-cols-5; }

/* Tablet Spacing */
.tablet-p-2 { @apply tablet:p-2; }
.tablet-p-3 { @apply tablet:p-3; }
.tablet-p-4 { @apply tablet:p-4; }
.tablet-p-6 { @apply tablet:p-6; }
.tablet-p-8 { @apply tablet:p-8; }

.tablet-m-2 { @apply tablet:m-2; }
.tablet-m-3 { @apply tablet:m-3; }
.tablet-m-4 { @apply tablet:m-4; }
.tablet-m-6 { @apply tablet:m-6; }

.tablet-gap-2 { @apply tablet:gap-2; }
.tablet-gap-3 { @apply tablet:gap-3; }
.tablet-gap-4 { @apply tablet:gap-4; }
.tablet-gap-6 { @apply tablet:gap-6; }

/* Tablet Typography */
.tablet-text-xs { @apply tablet:text-xs; }
.tablet-text-sm { @apply tablet:text-sm; }
.tablet-text-base { @apply tablet:text-base; }
.tablet-text-lg { @apply tablet:text-lg; }
.tablet-text-xl { @apply tablet:text-xl; }
.tablet-text-2xl { @apply tablet:text-2xl; }

/* Tablet Layout */
.tablet-flex { @apply tablet:flex; }
.tablet-flex-col { @apply tablet:flex-col; }
.tablet-flex-row { @apply tablet:flex-row; }
.tablet-hidden { @apply tablet:hidden; }
.tablet-block { @apply tablet:block; }
.tablet-inline-block { @apply tablet:inline-block; }

/* Tablet Widths */
.tablet-w-full { @apply tablet:w-full; }
.tablet-w-half { @apply tablet:w-1/2; }
.tablet-w-third { @apply tablet:w-1/3; }
.tablet-w-two-thirds { @apply tablet:w-2/3; }
.tablet-w-quarter { @apply tablet:w-1/4; }
.tablet-w-three-quarters { @apply tablet:w-3/4; }
.tablet-w-auto { @apply tablet:w-auto; }

/* Tablet Heights */
.tablet-h-auto { @apply tablet:h-auto; }
.tablet-h-full { @apply tablet:h-full; }
.tablet-h-screen { @apply tablet:h-screen; }

/* Touch-Friendly Sizing - Use inline styles instead */

/* Tablet-Specific Component Styles */
.tablet-card {
  @apply tablet:p-6 tablet:rounded-xl tablet:shadow-md;
}

.tablet-button {
  @apply tablet:px-6 tablet:py-3 tablet:text-base tablet:rounded-lg;
}

.tablet-input {
  @apply tablet:px-4 tablet:py-3 tablet:text-base tablet:rounded-lg;
}

/* Tablet Navigation */
.tablet-nav-item {
  @apply tablet:px-4 tablet:py-3 tablet:text-base tablet:rounded-lg;
}

.tablet-nav-icon {
  @apply tablet:w-6 tablet:h-6;
}

/* Tablet Tables */
.tablet-table {
  @apply tablet:text-sm;
}

.tablet-table-cell {
  @apply tablet:px-4 tablet:py-3;
}

.tablet-table-header {
  @apply tablet:px-4 tablet:py-3 tablet:text-sm tablet:font-semibold;
}

/* Tablet Forms */
.tablet-form-group {
  @apply tablet:mb-6;
}

.tablet-form-label {
  @apply tablet:text-sm tablet:font-medium tablet:mb-2;
}

.tablet-form-input {
  @apply tablet:w-full tablet:px-4 tablet:py-3 tablet:text-base tablet:border tablet:rounded-lg;
}

.tablet-form-select {
  @apply tablet:w-full tablet:px-4 tablet:py-3 tablet:text-base tablet:border tablet:rounded-lg;
}

/* Tablet Modals */
.tablet-modal {
  @apply tablet:max-w-2xl tablet:mx-auto tablet:my-8;
}

.tablet-modal-header {
  @apply tablet:px-6 tablet:py-4 tablet:border-b;
}

.tablet-modal-body {
  @apply tablet:px-6 tablet:py-4;
}

.tablet-modal-footer {
  @apply tablet:px-6 tablet:py-4 tablet:border-t;
}

/* Tablet Dropdowns */
.tablet-dropdown {
  @apply tablet:min-w-48 tablet:max-w-xs;
}

.tablet-dropdown-item {
  @apply tablet:px-4 tablet:py-3 tablet:text-sm;
}

/* Tablet Sidebar */
.tablet-sidebar {
  @apply tablet:w-64 tablet:min-w-64;
}

.tablet-sidebar-collapsed {
  @apply tablet:w-16 tablet:min-w-16;
}

/* Tablet Content Areas */
.tablet-content {
  @apply tablet:px-6 tablet:py-4;
}

.tablet-content-wide {
  @apply tablet:px-8 tablet:py-6;
}

/* Tablet Charts and Graphs */
.tablet-chart {
  @apply tablet:h-64;
}

.tablet-chart-large {
  @apply tablet:h-80;
}

/* Tablet Responsive Utilities */
.tablet-responsive-stack {
  @apply tablet:flex-col tablet:space-y-4;
}

.tablet-responsive-grid {
  @apply tablet:grid tablet:gap-4;
}

/* iPad Mini Specific */
@media (min-width: 768px) and (max-width: 1023px) {
  .ipad-mini-only {
    display: block;
  }
  
  .hide-ipad-mini {
    display: none;
  }
  
  .ipad-mini-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .ipad-mini-sidebar {
    width: 240px;
    min-width: 240px;
  }
}

/* iPad and Tablet Specific */
@media (min-width: 1024px) and (max-width: 1279px) {
  .ipad-only {
    display: block;
  }
  
  .hide-ipad {
    display: none;
  }
  
  .ipad-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
  
  .ipad-sidebar {
    width: 280px;
    min-width: 280px;
  }
}

/* Large Tablet Specific */
@media (min-width: 1280px) and (max-width: 1535px) {
  .large-tablet-only {
    display: block;
  }
  
  .hide-large-tablet {
    display: none;
  }
  
  .large-tablet-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
}

/* Orientation-specific styles */
@media (orientation: landscape) {
  .landscape-only {
    display: block;
  }
  
  .hide-landscape {
    display: none;
  }
  
  .landscape-flex {
    display: flex;
    flex-direction: row;
  }
}

@media (orientation: portrait) {
  .portrait-only {
    display: block;
  }
  
  .hide-portrait {
    display: none;
  }
  
  .portrait-flex {
    display: flex;
    flex-direction: column;
  }
}

/* Touch-specific improvements */
@media (hover: none) and (pointer: coarse) {
  .touch-device .hover-effect:hover {
    background-color: transparent;
  }
  
  .touch-device .touch-highlight {
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
  }
  
  .touch-device button,
  .touch-device .clickable {
    min-height: 44px;
    min-width: 44px;
  }
}
