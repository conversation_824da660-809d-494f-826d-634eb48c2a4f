/* Dashboard V2 Component Styles */

.dashboard-v2-container {
  @apply min-h-screen bg-gray-50;
}

/* KPI Cards */
.kpi-card {
  @apply bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300;
}

.kpi-card-header {
  @apply flex items-center justify-between mb-4;
}

.kpi-card-title {
  @apply text-sm font-medium text-gray-600;
}

.kpi-card-icon {
  @apply h-10 w-10 flex items-center justify-center rounded-full;
}

.kpi-card-value {
  @apply text-2xl font-bold text-gray-900;
}

/* Today Cards */
.today-card {
  @apply bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-300;
}

.today-card-header {
  @apply flex items-center justify-between mb-4;
}

.today-card-title {
  @apply text-sm font-medium text-gray-600;
}

.today-card-value {
  @apply text-2xl font-bold text-gray-900;
}

/* Chart Containers */
.chart-container {
  @apply bg-gray-50 rounded-lg p-4;
}

.chart-title {
  @apply text-lg font-semibold text-gray-800 mb-4;
}

.chart-wrapper {
  @apply h-64 relative;
}

/* Date Range Filter */
.date-filter-container {
  @apply mb-6;
}

.preset-buttons {
  @apply flex flex-wrap gap-2;
}

.preset-button {
  @apply px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
}

.preset-button-active {
  @apply bg-blue-500 text-white;
}

.preset-button-inactive {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.custom-date-picker {
  @apply flex items-center gap-2;
}

/* Service Details */
.service-details-container {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.service-search-container {
  @apply relative;
}

.service-search-input {
  @apply w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500;
}

.service-search-dropdown {
  @apply absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto;
}

.service-search-item {
  @apply px-4 py-2 hover:bg-gray-100 cursor-pointer;
}

.service-info-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.service-info-card {
  @apply rounded-lg p-4;
}

.service-info-card-blue {
  @apply bg-blue-50;
}

.service-info-card-green {
  @apply bg-green-50;
}

.service-info-card-red {
  @apply bg-red-50;
}

.service-info-card-yellow {
  @apply bg-yellow-50;
}

.service-info-title {
  @apply text-sm font-medium mb-2;
}

.service-info-value {
  @apply text-2xl font-bold;
}

/* Tables */
.data-table {
  @apply min-w-full divide-y divide-gray-200;
}

.table-header {
  @apply bg-gray-50;
}

.table-header-cell {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-body {
  @apply bg-white divide-y divide-gray-200;
}

.table-row {
  @apply hover:bg-gray-50;
}

.table-row-clickable {
  @apply hover:bg-gray-50 cursor-pointer;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-500;
}

.table-cell-primary {
  @apply px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900;
}

.table-cell-truncate {
  @apply px-6 py-4 text-sm text-gray-500 max-w-xs truncate;
}

/* Status Badges */
.status-badge {
  @apply px-2 py-1 text-xs font-medium rounded-full;
}

.status-completed {
  @apply bg-green-100 text-green-800;
}

.status-in-progress {
  @apply bg-blue-100 text-blue-800;
}

.status-pending {
  @apply bg-yellow-100 text-yellow-800;
}

.status-cancelled {
  @apply bg-red-100 text-red-800;
}

.status-refunded {
  @apply bg-red-100 text-red-800;
}

.status-default {
  @apply bg-gray-100 text-gray-800;
}

/* Pagination */
.pagination-container {
  @apply mt-6 flex items-center justify-between;
}

.pagination-info {
  @apply text-sm text-gray-700;
}

.pagination-buttons {
  @apply flex space-x-2;
}

.pagination-button {
  @apply px-3 py-2 text-sm font-medium border border-gray-300 rounded-md;
}

.pagination-button-active {
  @apply bg-blue-500 text-white;
}

.pagination-button-inactive {
  @apply bg-white text-gray-500 hover:bg-gray-50;
}

.pagination-button-disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* Loading States */
.loading-spinner {
  @apply animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500;
}

.loading-container {
  @apply flex justify-center items-center py-12;
}

/* Responsive Design */
/* Mobile (below 768px) */
@media (max-width: 767px) {
  .chart-wrapper {
    @apply h-48;
  }

  .service-info-grid {
    @apply grid-cols-1;
  }

  .preset-buttons {
    @apply flex-wrap gap-1;
  }

  .custom-date-picker {
    @apply flex-col items-start;
  }

  /* Better chart padding for mobile */
  .chart-container {
    @apply p-2;
  }

  /* Improved mobile chart heights */
  .chart-wrapper {
    @apply h-48;
  }

  /* Mobile tab navigation improvements */
  .tab-navigation {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Better mobile card spacing */
  .kpi-card,
  .today-card {
    @apply p-3;
  }
}

/* Tablet Mini (iPad Mini) - 768px to 1023px */
@media (min-width: 768px) and (max-width: 1023px) {
  .dashboard-v2-container {
    @apply p-4;
  }

  .chart-wrapper {
    @apply h-56;
  }

  .service-info-grid {
    @apply grid-cols-2 gap-4;
  }

  .chart-container {
    @apply p-4 rounded-xl;
  }

  .kpi-card,
  .today-card {
    @apply p-4 rounded-xl;
  }

  .kpi-grid {
    @apply grid-cols-2 gap-4;
  }

  .preset-buttons {
    @apply gap-2;
  }

  .preset-button {
    @apply px-3 py-2 text-sm;
  }

  .tab-navigation {
    @apply space-x-2;
  }

  .tab-button {
    @apply px-4 py-2 text-sm;
  }

  .table-cell,
  .table-cell-primary,
  .table-cell-truncate {
    @apply px-3 py-2 text-sm;
  }

  .table-header-cell {
    @apply px-3 py-2 text-sm;
  }
}

/* Tablet (iPad) - 1024px to 1279px */
@media (min-width: 1024px) and (max-width: 1279px) {
  .dashboard-v2-container {
    @apply p-6;
  }

  .chart-wrapper {
    @apply h-64;
  }

  .service-info-grid {
    @apply grid-cols-3 gap-5;
  }

  .chart-container {
    @apply p-5 rounded-xl;
  }

  .kpi-card,
  .today-card {
    @apply p-5 rounded-xl;
  }

  .kpi-grid {
    @apply grid-cols-3 gap-5;
  }

  .preset-buttons {
    @apply gap-3;
  }

  .preset-button {
    @apply px-4 py-2 text-base;
  }

  .tab-navigation {
    @apply space-x-3;
  }

  .tab-button {
    @apply px-5 py-2.5 text-base;
  }

  .table-cell,
  .table-cell-primary,
  .table-cell-truncate {
    @apply px-4 py-3 text-base;
  }

  .table-header-cell {
    @apply px-4 py-3 text-base;
  }
}

/* Large Tablet - 1280px and up */
@media (min-width: 1280px) {
  .dashboard-v2-container {
    @apply p-8;
  }

  .chart-wrapper {
    @apply h-72;
  }

  .service-info-grid {
    @apply grid-cols-4 gap-6;
  }

  .chart-container {
    @apply p-6 rounded-2xl;
  }

  .kpi-card,
  .today-card {
    @apply p-6 rounded-2xl;
  }

  .kpi-grid {
    @apply grid-cols-4 gap-6;
  }

  .preset-buttons {
    @apply gap-4;
  }

  .preset-button {
    @apply px-5 py-2.5 text-lg;
  }

  .tab-navigation {
    @apply space-x-4;
  }

  .tab-button {
    @apply px-6 py-3 text-lg;
  }

  .table-cell,
  .table-cell-primary,
  .table-cell-truncate {
    @apply px-5 py-3 text-base;
  }

  .table-header-cell {
    @apply px-5 py-3 text-base;
  }
}

/* Small screens */
@media (max-width: 640px) {
  .dashboard-v2-container {
    @apply p-2;
  }

  .chart-wrapper {
    @apply h-40;
  }

  /* Smaller chart containers for mobile */
  .chart-container {
    @apply p-1;
  }

  .table-cell,
  .table-cell-primary,
  .table-cell-truncate {
    @apply px-2 py-2 text-xs;
  }

  .table-header-cell {
    @apply px-2 py-2 text-xs;
  }

  /* Compact KPI cards for small screens */
  .kpi-card,
  .today-card {
    @apply p-2;
  }

  /* Better preset button sizing */
  .preset-button {
    @apply px-2 py-1 text-xs;
  }

  /* Improve horizontal scroll for tabs */
  .tab-navigation {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .tab-navigation::-webkit-scrollbar {
    display: none;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-in-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover Effects */
.hover-scale {
  @apply transition-transform duration-200;
}

.hover-scale:hover {
  @apply transform scale-105;
}

.hover-shadow {
  @apply transition-shadow duration-300;
}

.hover-shadow:hover {
  @apply shadow-lg;
}

/* Chart responsiveness improvements */
.chart-container canvas {
  max-width: 100%;
  height: auto !important;
}

/* Mobile chart scrolling */
@media (max-width: 768px) {
  .chart-wrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .chart-wrapper canvas {
    min-width: 300px;
  }
}

/* Tab navigation horizontal scroll */
.tab-scroll-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tab-scroll-container::-webkit-scrollbar {
  display: none;
}

/* Ensure charts are responsive */
.responsive-chart {
  position: relative;
  width: 100%;
  height: 100%;
}

.responsive-chart canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
}
