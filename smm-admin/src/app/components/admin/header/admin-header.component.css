:host {
  display: block;
  width: 100%;
}
.profile {
    @apply items-center gap-2 tablet-mini:gap-3 tablet:gap-4 p-2 tablet-mini:p-3 tablet:p-4 py-2 rounded-xl tablet:rounded-2xl border border-[#dcddff] hidden tablet-mini:flex bg-[var(--primary-light)] cursor-pointer hover:bg-blue-200 transition-all duration-200;
}

.header-container {
  position: sticky;
  top: 0;
  z-index: 30;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Desktop menu styling */
.desktop-menu {
  flex: 1 1 0%;
  margin-left: 1rem;
  margin-right: 1rem;
}

.menu-sections {
  justify-content: center;
  flex-wrap: wrap;
}

/* Dropdown menu styling */
.dropdown-menu {
  position: relative;
}

.dropdown-content {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-lg */
  border: 1px solid #f3f4f6; /* border border-gray-100 */
  opacity: 0;
  visibility: hidden;
  transition-property: all;
  transition-duration: 300ms;
  pointer-events: none;
  transform: scale(95%);
}

.group:hover .dropdown-content {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.dropdown-item {
  transition-property: color;
  transition-duration: 150ms;
}

.dropdown-item.active {
  background-color: #f0f7ff; /* bg-[#f0f7ff] */
  color: #3b82f6; /* text-[#3b82f6] */
  font-weight: 500; /* font-medium */
}

/* Add a delay to prevent accidental closing */
.group:hover .dropdown-content {
  transition-delay: 100ms; /* delay-100 */
}

/* Add a delay to the dropdown closing */
.dropdown-content {
  transition-delay: 0.1s;
}

/* Ensure the dropdown stays open when hovering over it */
.dropdown-content:hover {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

/* Style for the dropdown wrapper */
.dropdown-wrapper {
  transition-property: all;
  transition-duration: 200ms;
  display: none; /* Hide by default */
}

/* Show dropdown wrapper when group is hovered */
.group:hover .dropdown-wrapper {
  display: block;
}

/* Fix for dropdown hover behavior */
.group:hover .dropdown-content,
.dropdown-wrapper:hover .dropdown-content {
  opacity: 1;
  visibility: visible;
  transform: scale(100%);
  pointer-events: auto;
}

/* Make dropdown appear when hovering on dropdown-wrapper */
.dropdown-wrapper:hover {
  display: block;
}

/* Ensure dropdown-wrapper is visible when hovered */
.dropdown-wrapper:hover + .dropdown-content,
.dropdown-content:hover {
  opacity: 1;
  visibility: visible;
  transform: scale(100%);
  pointer-events: auto;
}

/* Original nav items */
.nav-item {
  color: #4b5563; /* text-gray-600 */
  transition-property: color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  padding-top: 0.25rem; /* py-1 */
  padding-bottom: 0.25rem; /* py-1 */
}

.nav-item:hover {
  color: #3b82f6; /* hover:text-[#3b82f6] */
}

.nav-item.active {
  color: var(--primary);
  font-weight: 600; /* font-semibold */
  background-color: #f0f7ff; /* bg-[#f0f7ff] */
}

.balance {
  display: flex; /* flex */
  align-items: center; /* items-center */
  gap: 0.5rem; /* gap-2 */
}

.amount {
  font-size: 0.875rem; /* text-sm */
  font-weight: 500; /* font-medium */
}

/* Balance button styling */
.balance-button {
  display: flex;
  align-items: center;
  background-color: white;
  border: 1px solid #f3f4f6;
  border-radius: 0.5rem;
  padding: 0.375rem 0.75rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.balance-button:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border-color: #e5e7eb;
}

/* Balance dropdown styling */
.right-section .dropdown-content {
  border-radius: 0.5rem;
  overflow: hidden;
}

/* Tablet responsiveness */
@media (min-width: 768px) and (max-width: 1023px) {
  .header-container {
    @apply mb-4;
  }

  .menu-bar {
    @apply px-4 py-3;
  }

  .left-section {
    @apply gap-3;
  }

  .right-section {
    @apply gap-2;
  }

  .profile {
    @apply gap-2 p-2 py-1.5;
  }

  .logo-container span {
    @apply text-lg;
  }
}

@media (min-width: 1024px) and (max-width: 1279px) {
  .header-container {
    @apply mb-5;
  }

  .menu-bar {
    @apply px-6 py-4;
  }

  .left-section {
    @apply gap-4;
  }

  .right-section {
    @apply gap-3;
  }

  .profile {
    @apply gap-3 p-3 py-2;
  }

  .logo-container span {
    @apply text-xl;
  }
}

@media (min-width: 1280px) {
  .header-container {
    @apply mb-6;
  }

  .menu-bar {
    @apply px-8 py-4;
  }

  .left-section {
    @apply gap-5;
  }

  .right-section {
    @apply gap-4;
  }

  .profile {
    @apply gap-4 p-4 py-2;
  }

  .logo-container span {
    @apply text-2xl;
  }
}

/* Mobile responsiveness for balance dropdown */
@media (max-width: 767px) {
  .right-section {
    gap: 0.5rem;
  }

  .balance-button {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }

  .right-section .dropdown-wrapper {
    width: 280px;
    right: -20px;
  }

  /* Adjust dropdown position for mobile */
  .right-section .group:first-child .dropdown-wrapper {
    right: -100px;
  }

  /* Make buttons smaller on mobile */
  .right-section button {
    padding: 0.375rem 0.5rem;
  }

  /* Reduce padding in dropdown content for mobile */
  .dropdown-content .p-4 {
    padding: 0.75rem;
  }
}

.user-dropdown {
  position: absolute; /* absolute */
  right: 0; /* right-0 */
  margin-top: 0.5rem; /* mt-2 */
  width: 12rem; /* w-48 */
  border-radius: 0.375rem; /* rounded-md */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-lg */
  padding-top: 0.25rem; /* py-1 */
  padding-bottom: 0.25rem; /* py-1 */
  background-color: white; /* bg-white */
  border: 1px solid rgba(0, 0, 0, 0.05); /* ring-1 ring-black ring-opacity-5 */
  outline: none; /* focus:outline-none */
}
