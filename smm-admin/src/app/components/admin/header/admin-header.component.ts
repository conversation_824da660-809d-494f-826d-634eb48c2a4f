import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsModule } from '../../../icons/icons.module';
import { RouterModule, Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SidebarService } from '../../../core/services/sidebar.service';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { AuthService } from '../../../core/services/auth.service';
import { AuthUtilsService } from '../../../core/services/auth-utils.service';
import { UserService } from '../../../core/services/user.service';
import { CurrencyService } from '../../../core/services/currency.service';
import { UserRes } from '../../../model/response/user-res.model';
import { Subscription } from 'rxjs';
import { TenantSwitcherComponent } from '../../tenant-switcher/tenant-switcher.component';
import { NotificationDropdownComponent } from '../notification-dropdown/notification-dropdown.component';
import { Role } from '../../../constant/role';

interface NavItem {
  icon: IconName;
  label: string;
  link: string;
  isActive: boolean;
  hideForChildPanel?: boolean; // New property to hide items for CHILD_PANEL role
}

interface NavSection {
  title: string;
  items: NavItem[];
}

@Component({
  selector: 'app-admin-header',
  standalone: true,
  imports: [CommonModule, IconsModule, RouterModule, TranslateModule, TenantSwitcherComponent, NotificationDropdownComponent],
  templateUrl: './admin-header.component.html',
  styleUrl: './admin-header.component.css'
})
export class AdminHeaderComponent implements OnInit, OnDestroy {
  @Input() isSidebarCollapsed: boolean = false;
  @Output() toggleSidebarCollapse = new EventEmitter<void>();

  user: UserRes | undefined;
  private userSubscription: Subscription | undefined;

  // Cache admin panel status to avoid repeated calls
  hasAdminPanelRole = false;
  hasChildPanelRole = false;
  filteredNavItems: NavSection[] = []; // Cache filtered items

  adminNavItems: NavSection[] = [
    {
      title: 'header.main_menu',
      items: [
        {
          icon: 'user' as IconName,
          label: 'header.users',
          link: '/panel/users',
          isActive: false
        },
        {
          icon: 'shopping-cart' as IconName,
          label: 'header.orders',
          link: '/panel/orders',
          isActive: false
        },
        {
          icon: 'tag' as IconName,
          label: 'header.services',
          link: '/panel/services',
          isActive: false
        },
        {
          icon: 'headset' as IconName,
          label: 'header.support',
          link: '/panel/tickets',
          isActive: false
        },

        {
          icon: 'chart-line' as IconName,
          label: 'header.statistics',
          link: '/panel/statistics',
          isActive: false
        },
        {
          icon: 'wallet' as IconName,
          label: 'header.add-funds',
          link: '/panel/add-funds',
          isActive: false,
          hideForChildPanel: true // Hide add-funds for CHILD_PANEL
        }
      ]
    },
    {
      title: 'header.more',
      items: [
        // {
        //   icon: 'chart-bar' as IconName,
        //   label: 'header.dashboard',
        //   link: '/panel/dashboard',
        //   isActive: false
        // },
        {
          icon: 'cog' as IconName,
          label: 'header.settings',
          link: '/panel/settings',
          isActive: false,
      
        },
        {
          icon: 'cog' as IconName,
          label: 'header.dripfeed',
          link: '/panel/dripfeed',
          isActive: false
        }
      ]
    }
  ];

  constructor(
    private sidebarService: SidebarService,
    private router: Router,
    private authService: AuthService,
    private authUtilsService: AuthUtilsService,
    private userService: UserService,
    private currencyService: CurrencyService
  ) {}

  ngOnInit() {
    this.updateActiveState();

    // Initialize role status first
    this.hasAdminPanelRole = this.authUtilsService.isAdminPanel();
    this.hasChildPanelRole = this.authUtilsService.isChildPanel();

    // Initialize filtered nav items
    this.initializeFilteredNavItems();

    // Subscribe to user information
    this.userSubscription = this.userService.user$.subscribe(user => {
      this.user = user;
      // Update admin panel role status when user changes
      this.hasAdminPanelRole = this.authUtilsService.isAdminPanel();
      this.hasChildPanelRole = this.authUtilsService.isChildPanel();

      // Re-initialize filtered nav items when user role changes
      this.initializeFilteredNavItems();
    });

    // Trigger getting user information if needed
    this.userService.get$.next();
  }

  ngOnDestroy() {
    // Clean up subscription when component is destroyed
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  updateActiveState() {
    const currentUrl = this.router.url;

    this.adminNavItems.forEach(section => {
      section.items.forEach(item => {
        // Check if the current URL starts with the item's link
        // This handles both exact matches and child routes (like /admin/tickets/123)
        if (item.link === '/panel/tickets') {
          item.isActive = currentUrl.startsWith(item.link);
        } else {
          item.isActive = currentUrl === item.link;
        }
      });
    });
  }

  toggleSidebar() {
    this.sidebarService.toggleSidebar();
  }

  onToggleSidebarCollapse() {
    this.toggleSidebarCollapse.emit();
  }

  getItemsBySection(sectionTitle: string): NavItem[] {
    // Map the hardcoded section titles to translation keys
    const sectionKeyMap: { [key: string]: string } = {
      'Main Menu': 'header.main_menu',
      'More': 'header.more'
    };

    const translationKey = sectionKeyMap[sectionTitle] || sectionTitle;
    // Use filtered nav items instead of original adminNavItems
    const section = this.getFilteredNavItems().find(section => section.title === translationKey);
    return section ? section.items : [];
  }

  // Navigate to profile page
  goToProfile(): void {
    this.router.navigate(['/panel/settings/profile']);
  }

  // Navigate to settings page
  goToSettings(): void {
    this.router.navigate(['/panel/settings']);
  }

  // Logout the user
  logout(): void {
    this.authService.logout();
  }

  // Navigate to add funds page
  goToAddFunds(): void {
    this.router.navigate(['/panel/add-funds']);
  }

  // Navigate to transaction history
  goToHistory(): void {
    this.router.navigate(['/panel/add-funds']);
  }

  // Navigate to commission info
  goToCommissionInfo(): void {
    this.router.navigate(['/panel/settings/commission']);
  }

  // Navigate to admin panel (only for ADMIN_PANEL users)
  goToAdmin(): void {
    this.router.navigate(['/admin']);
  }

  // Check if user has ADMIN_PANEL role (cached)
  isAdminPanel(): boolean {
    return this.hasAdminPanelRole;
  }

  // Check if user has CHILD_PANEL role (cached)
  isChildPanel(): boolean {
    return this.hasChildPanelRole;
  }

  // Initialize filtered nav items (called once or when role changes)
  private initializeFilteredNavItems(): void {
    if (!this.hasChildPanelRole) {
      this.filteredNavItems = this.adminNavItems; // Show all items for non-CHILD_PANEL users
    } else {
      // Filter out items that should be hidden for CHILD_PANEL
      this.filteredNavItems = this.adminNavItems.map(section => ({
        ...section,
        items: section.items.filter(item => !item.hideForChildPanel)
      })).filter(section => section.items.length > 0); // Remove empty sections
    }
  }

  // Get filtered nav items (cached)
  getFilteredNavItems(): NavSection[] {
    return this.filteredNavItems;
  }

  // Format balance using the common currency service
  formatBalance(balance: number | undefined): string {
    return this.currencyService.formatBalance(balance);
  }

    getAvatarPath(): string {
    if (!this.user || !this.user.avatar) {
      // Default avatar if no user or no avatar
      return 'assets/images/profile-1.png';
    }

    // Return the avatar path based on user.avatar
    return `assets/images/${this.user.avatar}.png`;
  }
}
