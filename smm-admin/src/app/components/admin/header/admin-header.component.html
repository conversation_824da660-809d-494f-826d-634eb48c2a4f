<div class="header-container mb-6">
  <div
    class="menu-bar flex items-center justify-between p-4 tablet-mini:px-6 tablet:px-8 bg-white border-b backdrop-filter backdrop-blur-sm bg-opacity-90">
    <!-- Left Section -->
    <div class="left-section flex items-center gap-3 tablet-mini:gap-4 tablet:gap-5">
      <!-- Toggle Button (Mobile only) -->
      <button class="tablet-mini:hidden text-[#3b82f6] hover:text-[#2563eb] transition-colors duration-200"
        style="min-height: 44px; min-width: 44px;"
        (click)="toggleSidebar()">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>

      <!-- Sidebar Collapse Toggle (Tablet and Desktop) -->
      <button class="hidden tablet-mini:flex text-[#3b82f6] hover:text-[#2563eb] transition-colors duration-200"
        style="min-height: 44px; min-width: 44px;"
        (click)="onToggleSidebarCollapse()"
        [title]="isSidebarCollapsed ? 'Expand Sidebar' : 'Collapse Sidebar'">
        <svg class="w-5 h-5 tablet:w-6 tablet:h-6 transition-transform duration-200"
             [class.rotate-180]="isSidebarCollapsed"
             fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
        </svg>
      </button>

      <!-- Logo - Responsive sizing -->
      <div class="hidden tablet-mini:flex ml-0 tablet-mini:ml-2 tablet:ml-4 items-center">
        <div class="logo-container flex items-center">
          <span class="text-lg tablet:text-xl tablet-large:text-2xl font-bold text-blue-600 transition-all duration-200">
            NEWPANEL
          </span>
        </div>
      </div>
    </div>

    <!-- Desktop Menu (visible only on md and above) -->
    <div class="desktop-menu hidden md:flex items-center justify-center">
      <div class="menu-sections flex gap-6">
        <!-- Main Menu Items -->
        <div *ngFor="let item of getItemsBySection('Main Menu')" class="dropdown-menu">
          <a [routerLink]="item.link" routerLinkActive="active"
            class="nav-item cursor-pointer font-medium py-2 block px-4 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 rounded-md transition-all duration-200">
            <span class="uppercase tracking-wide">{{ item.label | translate }}</span>
          </a>
        </div>

        <!-- More Dropdown -->
        <div class="dropdown-menu relative group" #moreDropdown>
          <button
            class="nav-item cursor-pointer font-medium flex items-center gap-1 py-2 px-4 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 rounded-md transition-all duration-200">
            <span class="uppercase tracking-wide">{{ 'header.more' | translate }}</span>
            <fa-icon [icon]="['fas', 'chevron-down']"
              class="text-xs ml-1 transition-transform duration-200 group-hover:rotate-180"></fa-icon>
          </button>
          <!-- Dropdown menu with improved hover behavior -->
          <div class="dropdown-wrapper absolute left-0 top-full w-48 z-50">
            <!-- Invisible bridge to maintain hover state -->
            <div class="h-2 w-full"></div>
            <!-- Dropdown content -->
            <div
              class="dropdown-content bg-white shadow-lg rounded-md py-2 w-full transition-all duration-200 transform origin-top scale-95">
              <a *ngFor="let item of getItemsBySection('More')" [routerLink]="item.link" routerLinkActive="active"
                class="dropdown-item block px-4 py-2 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[#3b82f6] transition-colors duration-150">
                <span>{{ item.label | translate }}</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Section -->
    <div class="right-section flex items-center gap-2 tablet-mini:gap-3 tablet:gap-4">
      <!-- Go to Admin Button (only for ADMIN_PANEL users) -->
      <button
        *ngIf="isAdminPanel()"
        (click)="goToAdmin()"
        class="inline-flex items-center px-2 tablet-mini:px-3 py-1.5 bg-purple-600 text-white text-xs tablet-mini:text-sm font-medium rounded-lg hover:bg-purple-700 transition-colors"
        style="min-height: 44px; min-width: 44px;">
        <fa-icon [icon]="['fas', 'shield-alt']" class="mr-1 tablet-mini:mr-2 text-xs tablet-mini:text-sm"></fa-icon>
        <span class="hidden tablet-mini:inline">{{ 'go_to_admin' | translate }}</span>
        <span class="tablet-mini:hidden">Admin</span>
      </button>

      <!-- Tenant Switcher - Hidden for CHILD_PANEL role -->
      <app-tenant-switcher *ngIf="!isChildPanel()" class="tablet-responsive"></app-tenant-switcher>

      <!-- Balance Dropdown - Hidden for CHILD_PANEL role -->
      <div class="relative group" *ngIf="!isChildPanel()">
        <button
          class="balance-button flex items-center space-x-2 focus:outline-none bg-white border border-gray-100 px-3 py-1.5 rounded-lg hover:shadow-sm transition-all duration-200">
          <div class="flex items-center">
            <div class="flex items-center mr-1">
              <fa-icon [icon]="['fas', 'wallet']" class="text-green-500 mr-1"></fa-icon>
              <span class="text-sm font-medium text-gray-800">${{ formatBalance(user?.balance) }}</span>
            </div>
            <fa-icon [icon]="['fas', 'chevron-down']" class="text-xs text-gray-500 hidden md:block"></fa-icon>
          </div>
        </button>
        <!-- Balance Dropdown Content -->
        <div class="dropdown-wrapper absolute right-0 top-full w-72 z-50">
          <!-- Invisible bridge to maintain hover state -->
          <div class="h-2 w-full"></div>
          <!-- Dropdown content -->
          <div
            class="dropdown-content bg-white shadow-lg rounded-lg overflow-hidden w-full transition-all duration-200 transform origin-top scale-95">
            <!-- Balance Header -->
            <div class="p-4 bg-white border-b">
              <div class="text-center">
                <div class="text-2xl font-bold text-green-500">${{ formatBalance(user?.balance) }}</div>
                <div class="text-sm text-gray-500">{{ 'header.current_balance' | translate }}</div>
              </div>
            </div>

            <!-- Panel Pricing Info -->
            <div class="p-4 bg-white border-b">
              <div class="text-center mb-3">
                <div class="text-lg font-semibold text-blue-600">{{ 'header.panel_pricing' | translate }}</div>
                <div class="text-2xl font-bold text-green-500">$7.7</div>
                <div class="text-sm text-gray-500">{{ 'header.per_month' | translate }}</div>
              </div>

              <div class="bg-blue-50 rounded-lg p-3">
                <div class="flex items-center justify-center text-sm text-blue-700">
                  <fa-icon [icon]="['fas', 'info-circle']" class="mr-2"></fa-icon>
                  <span>{{ 'header.affordable_subscription' | translate }}</span>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="p-4 bg-white flex items-center justify-between">
              <button (click)="goToAddFunds()"
                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm flex items-center transition-colors duration-200">
                <fa-icon [icon]="['fas', 'plus']" class="mr-2"></fa-icon>
                {{ 'header.add_funds' | translate }}
              </button>
              <button (click)="goToHistory()"
                class="text-blue-500 hover:text-blue-600 px-4 py-2 text-sm transition-colors duration-200">
                {{ 'header.history' | translate }}
              </button>
            </div>


          </div>
        </div>
      </div>

      <!-- Notifications -->
      <app-notification-dropdown></app-notification-dropdown>

      <!-- User dropdown - Responsive design -->
      <div class="relative group">
        <div class="profile cursor-pointer focus:outline-none" style="min-height: 44px; min-width: 44px;">
          <div class="avatar">
            <img [src]="getAvatarPath()"
                 alt="Profile"
                 class="w-8 h-8 tablet-mini:w-10 tablet-mini:h-10 tablet:w-12 tablet:h-12 rounded-full border-2 border-[var(--primary)] transition-all duration-200">
          </div>
          <div class="user-info hidden tablet-mini:block">
            <span class="font-semibold text-sm tablet:text-base block truncate max-w-32 tablet:max-w-40">
              {{ user?.user_name }}
            </span>
          </div>
        </div>

        <!-- Dropdown menu with improved hover behavior -->
        <div class="dropdown-wrapper absolute right-0 top-full w-48 z-50">
          <!-- Invisible bridge to maintain hover state -->
          <div class="h-2 w-full"></div>
          <!-- Dropdown content -->
          <div
            class="dropdown-content bg-white shadow-lg rounded-md py-2 w-full transition-all duration-200 transform origin-top scale-95">
            <button *ngIf="!isChildPanel()" (click)="goToProfile()"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[#3b82f6]">
              {{ 'header.profile' | translate }}
            </button>
            <button (click)="goToSettings()"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[#3b82f6]">
              {{ 'header.settings' | translate }}
            </button>
            <button (click)="logout()"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[#3b82f6]">
              {{ 'header.logout' | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>