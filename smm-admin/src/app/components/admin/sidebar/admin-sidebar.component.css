:host {
  @apply block h-full;
}

/* Menu section styling */
.menu-section {
  @apply mb-6;
}

.section-title {
  @apply text-gray-500 text-sm font-medium uppercase mb-4 px-3 tracking-wider;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  border-left: 3px solid #3b82f6;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border-radius: 0 4px 4px 0;
}

.menu-list {
  @apply list-none p-0 m-0 flex flex-col gap-2;
}

.menu-item {
  @apply flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-all duration-200 text-gray-600;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  position: relative;
  overflow: hidden;
}

.menu-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  transition: width 0.3s ease;
  z-index: -1;
}

.menu-item:hover::before {
  width: 100%;
}

.menu-item.active {
  @apply bg-gradient-to-r from-blue-50 to-indigo-50 text-[var(--primary)] shadow-sm;
  transform: translateX(5px);
}

.menu-label {
  @apply text-sm font-medium transition-all duration-200;
}

.menu-label.active {
  @apply text-[var(--primary)] font-semibold;
}

.menu-item:hover {
  @apply text-[var(--primary)];
}

/* Logo styling */
.logo-container {
  @apply flex items-center justify-center p-3 tablet-mini:p-4 mb-3 tablet-mini:mb-4 transition-all duration-300;
  background: linear-gradient(135deg, #f0f7ff 0%, #e6eeff 100%);
  border-radius: 0 0 0.75rem 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
}

.logo-container.collapsed {
  @apply p-2 mb-2;
  border-radius: 0 0 0.5rem 0.5rem;
}

.logo-text {
  @apply text-lg tablet-mini:text-xl tablet:text-2xl font-bold text-blue-600 transition-all duration-300;
}

.logo-icon {
  @apply text-base tablet-mini:text-lg font-bold text-blue-600 transition-all duration-300;
}

.logo {
  @apply h-12 tablet-mini:h-16 w-auto object-contain transition-all duration-300;
}

/* Menu styling */
.menu-sidebar {
  @apply flex-1 overflow-y-auto px-3 tablet-mini:px-4 py-2 transition-all duration-300;
}

.menu-section {
  @apply mb-4 tablet-mini:mb-6;
}

.section-title {
  @apply text-xs tablet-mini:text-sm font-semibold text-gray-500 uppercase tracking-wide mb-2 tablet-mini:mb-3 px-2 transition-all duration-300;
}

.menu-list {
  @apply space-y-1 tablet-mini:space-y-2;
}

.menu-item {
  @apply flex items-center px-2 tablet-mini:px-3 py-2 tablet-mini:py-3 rounded-lg cursor-pointer transition-all duration-200 hover:bg-blue-50;
  min-height: 44px; /* Touch-friendly */
}

.menu-item.collapsed {
  @apply justify-center px-2 py-3;
}

.menu-item.active {
  @apply bg-blue-100 text-blue-600;
}

.menu-item:hover {
  @apply bg-blue-50;
}

.menu-item.active:hover {
  @apply bg-blue-100;
}

.icon-container {
  @apply flex items-center justify-center w-6 h-6 tablet-mini:w-7 tablet-mini:h-7;
}

.menu-icon {
  @apply text-base tablet-mini:text-lg mr-0 tablet-mini:mr-3 transition-all duration-200;
}

.menu-item.collapsed .menu-icon {
  @apply mr-0;
}

.menu-label {
  @apply text-sm tablet-mini:text-base font-medium truncate transition-all duration-200;
}

.menu-label.active {
  @apply font-semibold;
}

/* Footer styling */
.sidebar-footer {
  @apply p-3 tablet-mini:p-4 text-center transition-all duration-300;
}

.sidebar-footer.collapsed {
  @apply p-2;
}

.footer-content {
  @apply flex flex-col items-center;
}

.copyright, .version {
  @apply text-xs text-gray-500 mb-1;
}

.footer-icon {
  @apply flex items-center justify-center;
}

/* Responsive styles */
@media (max-width: 767px) {
  .sidebar {
    @apply fixed top-0 left-0 z-50 w-72 h-full bg-white shadow-lg transform -translate-x-full transition-all duration-300;
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  }

  .sidebar.open {
    @apply translate-x-0;
  }

  .sidebar > .flex-container {
    @apply p-0 h-full;
  }

  .logo {
    @apply h-7;
  }

  .menu-sidebar {
    @apply px-4 py-2;
  }
}

/* Tablet Mini (iPad Mini) - 768px to 1023px */
@media (min-width: 768px) and (max-width: 1023px) {
  .sidebar {
    @apply block relative w-64 min-w-64 h-full bg-white shadow-sm border-r border-gray-200 transition-all duration-300;
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  }

  .sidebar.collapsed {
    @apply w-16 min-w-16;
  }

  .menu-sidebar {
    @apply px-3 py-2;
  }

  .menu-item {
    @apply px-2 py-2;
  }

  .menu-icon {
    @apply text-base mr-2;
  }

  .menu-item.collapsed .menu-icon {
    @apply mr-0;
  }
}

/* Tablet (iPad) - 1024px to 1279px */
@media (min-width: 1024px) and (max-width: 1279px) {
  .sidebar {
    @apply block relative w-72 min-w-72 h-full bg-white shadow-sm border-r border-gray-200 transition-all duration-300;
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  }

  .sidebar.collapsed {
    @apply w-20 min-w-20;
  }

  .menu-sidebar {
    @apply px-4 py-3;
  }

  .menu-item {
    @apply px-3 py-3;
  }

  .menu-icon {
    @apply text-lg mr-3;
  }

  .menu-item.collapsed .menu-icon {
    @apply mr-0;
  }
}

/* Large Tablet - 1280px and up */
@media (min-width: 1280px) {
  .sidebar {
    @apply block relative w-80 min-w-80 h-full bg-white shadow-sm border-r border-gray-200 transition-all duration-300;
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  }

  .sidebar.collapsed {
    @apply w-24 min-w-24;
  }

  .menu-sidebar {
    @apply px-6 py-4;
  }

  .menu-item {
    @apply px-4 py-3;
  }

  .menu-icon {
    @apply text-xl mr-4;
  }

  .menu-item.collapsed .menu-icon {
    @apply mr-0;
  }
}
