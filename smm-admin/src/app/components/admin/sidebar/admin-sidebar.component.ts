import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { AppAssetsService } from '../../../core/services/app-assets.service';
import { Subscription } from 'rxjs';
import { AuthUtilsService } from '../../../core/services/auth-utils.service';

interface SideNavItem {
  icon: IconName;
  label: string;
  link: string;
  isActive: boolean;
  hideForChildPanel?: boolean; // New property to hide items for CHILD_PANEL role
}

interface SideNavSection {
  title: string;
  items: SideNavItem[];
}

@Component({
  selector: 'app-admin-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule, IconsModule, TranslateModule],
  templateUrl: './admin-sidebar.component.html',
  styleUrl: './admin-sidebar.component.css'
})
export class AdminSidebarComponent implements OnInit, OnDestroy {
  @Input() isOpen: boolean = true;
  @Input() isCollapsed: boolean = false;
  //logoUrl: string = 'assets/images/logo.png'; // Keep the default logo for admin pages
  private subscription = new Subscription();
  hasChildPanelRole = false;
  filteredNavItems: SideNavSection[] = []; // Cache filtered items

  adminNavItems: SideNavSection[] = [
    {
      title: 'admin.main_menu',
      items: [
        {
          icon: 'user' as IconName,
          label: 'admin.nav.users',
          link: '/panel/users',
          isActive: false
        },
        {
          icon: 'shopping-cart' as IconName,
          label: 'admin.nav.orders',
          link: '/panel/orders',
          isActive: false
        },
        {
          icon: 'clock' as IconName,
          label: 'admin.nav.dripfeed',
          link: '/panel/dripfeed',
          isActive: false
        },
        {
          icon: 'tag' as IconName,
          label: 'admin.nav.services',
          link: '/panel/services',
          isActive: false
        },
        {
          icon: 'headset' as IconName,
          label: 'admin.nav.support',
          link: '/panel/tickets',
          isActive: false
        },
 
        {
          icon: 'chart-line' as IconName,
          label: 'admin.nav.statistics',
          link: '/panel/statistics',
          isActive: false
        },
        {
           icon: 'wallet' as IconName,
          label: 'admin.nav.add-funds',
          link: '/panel/add-funds',
          isActive: false,
          hideForChildPanel: true // Hide add-funds for CHILD_PANEL
        }
      ]
    },
    {
      title: 'admin.nav.more',
      items: [
        {
          icon: 'chart-bar' as IconName,
          label: 'admin.nav.dashboard',
          link: '/panel/dashboard',
          isActive: false
        },
        {
          icon: 'cog' as IconName,
          label: 'admin.nav.settings',
          link: '/panel/settings',
          isActive: false,
         // hideForChildPanel: true // Hide settings for CHILD_PANEL
        }
      ]
    }
  ];

  constructor(
    private router: Router,
    private authUtilsService: AuthUtilsService
  ) {}

  ngOnInit() {
    // Check if user has CHILD_PANEL role
    this.hasChildPanelRole = this.authUtilsService.isChildPanel();

    // Initialize filtered nav items
    this.initializeFilteredNavItems();

    this.updateActiveState();
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  updateActiveState() {
    const currentUrl = this.router.url;

    this.adminNavItems.forEach(section => {
      section.items.forEach(item => {
        // Check if the current URL starts with the item's link
        // This handles both exact matches and child routes (like /admin/tickets/123)
        if (item.link === '/panel/tickets') {
          item.isActive = currentUrl.startsWith(item.link);
        } else {
          item.isActive = currentUrl === item.link;
        }
      });
    });
  }

  // Initialize filtered nav items (called once)
  private initializeFilteredNavItems(): void {
    if (!this.hasChildPanelRole) {
      this.filteredNavItems = this.adminNavItems; // Show all items for non-CHILD_PANEL users
    } else {
      // Filter out items that should be hidden for CHILD_PANEL
      this.filteredNavItems = this.adminNavItems.map(section => ({
        ...section,
        items: section.items.filter(item => !item.hideForChildPanel)
      })).filter(section => section.items.length > 0); // Remove empty sections
    }
  }

  // Get filtered nav items (cached)
  getFilteredNavItems(): SideNavSection[] {
    return this.filteredNavItems;
  }

  // Check if user has CHILD_PANEL role
  isChildPanel(): boolean {
    return this.hasChildPanelRole;
  }
}
