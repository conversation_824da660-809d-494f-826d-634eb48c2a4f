<!-- Sidebar component -->
<div class="sidebar" [class.open]="isOpen" [class.collapsed]="isCollapsed">
  <div class="flex-container flex flex-col h-full">
    <!-- Logo Section -->
    <div class="logo-container" [class.collapsed]="isCollapsed">
      <div class="flex items-center justify-center">
        <span class="logo-text" [class.hidden]="isCollapsed">NEWPANEL</span>
        <span class="logo-icon" [class.hidden]="!isCollapsed">NP</span>
      </div>
    </div>

    <!-- Menu -->
    <div class="menu-sidebar">
      <div *ngFor="let section of getFilteredNavItems()" class="menu-section">
        <h3 class="section-title" [class.hidden]="isCollapsed">{{ section.title | translate }}</h3>
        <ul class="menu-list">
          <li *ngFor="let item of section.items"
              class="menu-item"
              style="min-height: 44px; min-width: 44px;"
              [class.active]="item.isActive"
              [class.collapsed]="isCollapsed"
              [routerLink]="item.link"
              routerLinkActive="active"
              [title]="isCollapsed ? (item.label | translate) : ''">
              <div class="icon-container relative">
                <fa-icon
                  [icon]="['fas', item.icon]"
                  [ngClass]="{'text-[#3b82f6]': item.isActive, 'text-gray-500': !item.isActive}"
                  class="menu-icon flex-shrink-0 transition-all duration-200"
                ></fa-icon>
                <span *ngIf="item.isActive && !isCollapsed" class="absolute -bottom-1 -right-1 w-2 h-2 bg-[#3b82f6] rounded-full"></span>
              </div>
              <span class="menu-label transition-all duration-200"
                    [class.active]="item.isActive"
                    [class.hidden]="isCollapsed">
                {{ item.label | translate }}
              </span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Footer -->
    <div class="sidebar-footer mt-auto" [class.collapsed]="isCollapsed">
      <div class="footer-content">
        <p class="copyright" [class.hidden]="isCollapsed">{{ 'admin.copyright' | translate }}</p>
        <p class="version" [class.hidden]="isCollapsed">{{ 'admin.version' | translate }}</p>
        <div class="footer-icon" [class.hidden]="!isCollapsed">
          <fa-icon [icon]="['fas', 'info-circle']" class="text-gray-400"></fa-icon>
        </div>
      </div>
    </div>
  </div>
</div>
