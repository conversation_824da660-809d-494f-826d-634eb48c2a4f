<div class="layout flex h-screen">
  <div class="w-full flex">
    <!-- Sidebar (visible on tablets and larger) -->
    <app-admin-sidebar
      [isOpen]="isOpen"
      class="hidden tablet-mini:block tablet-mini:w-64 tablet:w-72 tablet-large:w-80 flex-shrink-0 transition-all duration-300"
      [class.tablet-sidebar-collapsed]="isSidebarCollapsed && !isOpen">
    </app-admin-sidebar>

    <!-- Mobile Sidebar Overlay -->
    <app-admin-sidebar [isOpen]="isOpen" class="tablet-mini:hidden"></app-admin-sidebar>

    <!-- Overlay (Mobile and small tablet only) -->
    <div *ngIf="isOpen"
         class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-40 tablet-mini:hidden transition-all duration-300"
         (click)="closeSidebar()"></div>

    <!-- Content -->
    <div class="content-layout flex-1 flex flex-col min-w-0">
      <app-admin-header
        [isSidebarCollapsed]="isSidebarCollapsed"
        (toggleSidebarCollapse)="toggleSidebarCollapse()">
      </app-admin-header>

      <main class="flex-1 overflow-y-auto mx-2 tablet-mini:mx-4 tablet:mx-6 tablet-large:mx-8 my-0 transition-all duration-300">
        <div class="tablet-content tablet-large:tablet-content-wide">
          <router-outlet></router-outlet>
        </div>
      </main>

      <!-- Live Chat Component - Hidden for CHILD_PANEL role -->
      <app-chat *ngIf="!isChildPanel()"
                class="tablet-mini:bottom-4 tablet-mini:right-4 tablet:bottom-6 tablet:right-6">
      </app-chat>
    </div>
  </div>
</div>
