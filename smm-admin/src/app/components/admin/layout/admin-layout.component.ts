import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AdminHeaderComponent } from '../header/admin-header.component';
import { AdminSidebarComponent } from '../sidebar/admin-sidebar.component';
import { SidebarService } from '../../../core/services/sidebar.service';
import { SharedModule } from '../../../shared/shared.module';
import { ChatComponent } from '../../../shared/components/chat/chat.component';
import { AuthUtilsService } from '../../../core/services/auth-utils.service';


@Component({
  selector: 'app-admin-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    AdminHeaderComponent,
    AdminSidebarComponent,
    ChatComponent,
    SharedModule
  ],
  templateUrl: './admin-layout.component.html',
  styleUrl: './admin-layout.component.css'
})
export class AdminLayoutComponent implements OnInit {
  isOpen: boolean = true;
  isSidebarCollapsed: boolean = false;
  hasChildPanelRole = false;

  constructor(
    private sidebarService: SidebarService,
    private authUtilsService: AuthUtilsService
  ) {}

  ngOnInit() {
    this.sidebarService.sidebarOpen$.subscribe(state => {
      this.isOpen = state;
    });

    // Check if user has CHILD_PANEL role
    this.hasChildPanelRole = this.authUtilsService.isChildPanel();

    // Initialize sidebar collapsed state based on screen size
    this.initializeSidebarState();
  }

  private initializeSidebarState() {
    // Check if we're on a tablet device and set initial collapsed state
    if (window.innerWidth >= 768 && window.innerWidth < 1280) {
      this.isSidebarCollapsed = false; // Keep expanded on tablets by default
    }
  }

  toggleSidebar() {
    this.sidebarService.toggleSidebar();
  }

  toggleSidebarCollapse() {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
  }

  closeSidebar() {
    if (this.isOpen) {
      this.sidebarService.setSidebarState(false);
    }
  }

  // Check if user has CHILD_PANEL role
  isChildPanel(): boolean {
    return this.hasChildPanelRole;
  }
}
