.content-layout {
  @apply flex-1 w-full min-w-0;
}

/* Tablet Mini (iPad Mini) - 768px to 1023px */
@media (min-width: 768px) and (max-width: 1023px) {
  .layout {
    @apply w-full h-full;
  }

  .content-layout {
    @apply w-full;
  }

  .layout-main {
    @apply px-4 py-3;
  }

  /* Sidebar adjustments for tablet mini */
  app-admin-sidebar {
    @apply w-64 min-w-64;
  }
}

/* Tablet (iPad) - 1024px to 1279px */
@media (min-width: 1024px) and (max-width: 1279px) {
  .layout {
    @apply w-full h-full;
  }

  .content-layout {
    @apply w-full;
  }

  .layout-main {
    @apply px-6 py-4;
  }

  /* Sidebar adjustments for tablet */
  app-admin-sidebar {
    @apply w-72 min-w-72;
  }
}

/* Large Tablet - 1280px and up */
@media (min-width: 1280px) {
  .layout {
    @apply w-full h-full;
  }

  .content-layout {
    @apply w-full;
  }

  .layout-main {
    @apply px-8 py-6;
  }

  /* Sidebar adjustments for large tablet */
  app-admin-sidebar {
    @apply w-80 min-w-80;
  }
}

/* Mobile - below 768px */
@media (max-width: 767px) {
  .main-content {
    @apply flex-col;
  }

  .layout-main {
    @apply px-3;
  }
}

.layout {
  @apply flex h-full;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9f2ff 100%);
}

.layout-main {
  @apply p-6 rounded-tl-xl;
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

/* Sidebar collapsed state */
.tablet-sidebar-collapsed {
  @apply tablet:w-16 tablet:min-w-16;
}

/* Smooth transitions for sidebar */
app-admin-sidebar {
  transition: width 0.3s ease, min-width 0.3s ease;
}

/* Content area adjustments when sidebar is collapsed */
.content-layout {
  transition: margin-left 0.3s ease;
}

/* Chat component positioning for tablets */
app-chat {
  @apply fixed bottom-4 right-4 z-50;
  transition: bottom 0.3s ease, right 0.3s ease;
}

/* Responsive chat positioning */
@media (min-width: 768px) {
  app-chat {
    @apply bottom-6 right-6;
  }
}

@media (min-width: 1024px) {
  app-chat {
    @apply bottom-8 right-8;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #2563eb;
}
