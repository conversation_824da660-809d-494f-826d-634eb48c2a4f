/**
 * Main Styles
 * This is the main stylesheet that imports all other stylesheets
 */

/* Import CSS variables first */
@import './app/shared/styles/variables.css';

/* Import utility classes */
@import './app/shared/styles/utils.css';

/* Import common layout and utility classes */
@import './app/shared/styles/common.css';

/* Import component styles */
@import './app/shared/styles/components.css';

/* Import tablet responsive utilities */
@import './app/shared/styles/tablet-responsive.css';

/* Import tablet form components */
@import './app/shared/styles/tablet-forms.css';



/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Toast Notifications */
.toast-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-weight: 500;
  z-index: 9999;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(100px);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.toast-notification.show {
  transform: translateY(0);
  opacity: 1;
}

.toast-success {
  background-color: #28a745;
}

.toast-warning {
  background-color: #ffc107;
  color: #212529;
}

.toast-error {
  background-color: #dc3545;
}

/* Base styles */
@layer base {
  body {
    @apply bg-[var(--background)] font-[var(--font-family-roboto)] text-[var(--gray-800)] text-sm;
  }

  input, select, textarea {
    @apply outline-none border border-[var(--gray-200)] rounded-[var(--radius-md)] p-3 text-sm text-[var(--gray-800)];
  }

  input:focus, select:focus, textarea:focus {
    @apply border-[var(--primary)];
  }

  /* Remove spinner buttons from number inputs - Chrome, Safari, Edge, Opera */
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Remove spinner buttons from number inputs - Firefox */
  input[type="number"] {
    -moz-appearance: textfield;
  }

  thead {
    @apply rounded-none;
  }

  table {
    @apply border-collapse w-full border-t-0;
  }

  th {
    @apply bg-gray-50 px-3 py-4 text-left font-medium text-[var(--gray-800)] border border-[var(--gray-200)];
  }

  td {
    @apply p-3 border border-[var(--gray-200)];
  }

  h2 {
    @apply font-bold text-2xl;
  }
}

/* Custom components */
@layer components {
  .checkbox-label {
    @apply w-4 h-4 text-blue-600 bg-gray-100 border-gray-900 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600;
  }

  .btn-submit {
    @apply bg-[var(--primary)] text-white rounded-2xl px-[22px] py-3 font-bold text-base  flex items-center justify-center cursor-pointer;
  }


  .btn-submit span:first-child {
    @apply font-medium;
  }

  .btn-submit span:last-child {
    @apply shadow-sm border border-[rgba(255,255,255,0.2)] min-w-[80px] text-center;
  }

  .btn-mess-outline {
    @apply text-[var(--primary)] font-semibold border border-[var(--primary)] rounded-xl w-full px-[22px] py-4;
  }
}

/* Modal Host Styles */
.modal-host {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}
